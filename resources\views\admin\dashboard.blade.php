@extends('layouts.app')

@section('content')
<h2>Admin Dashboard</h2>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>Register New User</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.register-user') }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-control" id="role" name="role" required>
                            <option value="">Select Role</option>
                            <option value="admin">Admin</option>
                            <option value="teacher">Teacher</option>
                            <option value="student">Student</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3" id="subjects-section" style="display: none;">
                        <label class="form-label">Subjects (for Teachers)</label>
                        @foreach(\App\Models\Subject::all() as $subject)
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="subjects[]" value="{{ $subject->id }}" id="subject{{ $subject->id }}">
                            <label class="form-check-label" for="subject{{ $subject->id }}">
                                {{ $subject->name }}
                            </label>
                        </div>
                        @endforeach
                    </div>
                    <button type="submit" class="btn btn-success">Register User</button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>Create Class</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.create-class') }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label for="class_name" class="form-label">Class Name</label>
                        <input type="text" class="form-control" id="class_name" name="name" placeholder="e.g., Class 2 East" required>
                    </div>
                    <button type="submit" class="btn btn-success">Create Class</button>
                </form>
                <div class="mt-2">
                    <a href="{{ route('admin.manage-classes') }}" class="btn btn-info btn-sm">Manage Classes</a>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5>Create Subject</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('admin.create-subject') }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label for="subject_name" class="form-label">Subject Name</label>
                        <input type="text" class="form-control" id="subject_name" name="name" placeholder="e.g., Mathematics" required>
                    </div>
                    <button type="submit" class="btn btn-success">Create Subject</button>
                </form>
                <div class="mt-2">
                    <a href="{{ route('admin.manage-subjects') }}" class="btn btn-info btn-sm">Manage Subjects</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="mt-4">
    <a href="{{ route('admin.users') }}" class="btn btn-info">Manage Users & Classes</a>
</div>

<script>
document.getElementById('role').addEventListener('change', function() {
    const subjectsSection = document.getElementById('subjects-section');
    if (this.value === 'teacher') {
        subjectsSection.style.display = 'block';
    } else {
        subjectsSection.style.display = 'none';
    }
});
</script>
@endsection
